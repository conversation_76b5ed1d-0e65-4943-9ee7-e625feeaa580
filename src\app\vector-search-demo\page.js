"use client";

import React, { useState } from "react";
import VectorSearchBar from "@/app/components/VectorSearchBar";
import { searchProducts } from "@/server/typesense/search";
import { useQuery } from "@tanstack/react-query";

export default function VectorSearchDemo() {
  const [demoQuery, setDemoQuery] = useState("");
  const [searchType, setSearchType] = useState("hybrid");
  const [showComparison, setShowComparison] = useState(false);

  // Example queries to demonstrate vector search capabilities
  const exampleQueries = [
    {
      query: "fast gaming laptop",
      description: "Semantic search for gaming laptops",
    },
    {
      query: "budget smartphone with good camera",
      description: "Natural language product search",
    },
    {
      query: "wireless headphones for exercise",
      description: "Context-aware search",
    },
    {
      query: "professional video editing workstation",
      description: "Intent-based search",
    },
  ];

  // Comparison search results
  const { data: textResults, isLoading: textLoading } = useQuery({
    queryKey: ["comparison-text", demoQuery],
    queryFn: () =>
      searchProducts(demoQuery, {
        searchType: "text",
        limit: 5,
        includeFields: "id,name,description,price,priceOld,featuredImage",
      }),
    enabled: showComparison && demoQuery.length > 2,
  });

  const { data: vectorResults, isLoading: vectorLoading } = useQuery({
    queryKey: ["comparison-vector", demoQuery],
    queryFn: () =>
      searchProducts(demoQuery, {
        searchType: "vector",
        limit: 5,
        includeFields: "id,name,description,price,priceOld,featuredImage",
      }),
    enabled: showComparison && demoQuery.length > 2,
  });

  const { data: hybridResults, isLoading: hybridLoading } = useQuery({
    queryKey: ["comparison-hybrid", demoQuery],
    queryFn: () =>
      searchProducts(demoQuery, {
        searchType: "hybrid",
        limit: 5,
        includeFields: "id,name,description,price,priceOld,featuredImage",
      }),
    enabled: showComparison && demoQuery.length > 2,
  });

  const handleExampleQuery = (query) => {
    setDemoQuery(query);
    setShowComparison(true);
  };

  const renderSearchResults = (
    results,
    isLoading,
    title,
    bgColor,
    iconColor,
    borderColor
  ) => {
    if (isLoading) {
      return (
        <div
          className={`${bgColor} border-2 ${borderColor} p-6 rounded-xl shadow-lg`}
        >
          <div className="flex items-center mb-4">
            <div
              className={`w-10 h-10 ${iconColor} rounded-lg flex items-center justify-center mr-3 shadow-md`}
            >
              <span className="text-white font-bold text-lg">
                {title.includes("Text")
                  ? "📝"
                  : title.includes("Vector")
                  ? "🧠"
                  : "🔄"}
              </span>
            </div>
            <h3 className="font-bold text-lg text-gray-800">{title}</h3>
          </div>
          <div className="animate-pulse space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-gray-200 h-20 rounded-lg"></div>
            ))}
          </div>
        </div>
      );
    }

    if (!results?.success || !results?.data?.hits?.length) {
      return (
        <div
          className={`${bgColor} border-2 ${borderColor} p-6 rounded-xl shadow-lg`}
        >
          <div className="flex items-center mb-4">
            <div
              className={`w-10 h-10 ${iconColor} rounded-lg flex items-center justify-center mr-3 shadow-md`}
            >
              <span className="text-white font-bold text-lg">
                {title.includes("Text")
                  ? "📝"
                  : title.includes("Vector")
                  ? "🧠"
                  : "🔄"}
              </span>
            </div>
            <h3 className="font-bold text-lg text-gray-800">{title}</h3>
          </div>
          <div className="text-center py-8">
            <div className="text-gray-400 text-4xl mb-2">🔍</div>
            <p className="text-gray-500 font-medium">No results found</p>
          </div>
        </div>
      );
    }

    return (
      <div
        className={`${bgColor} border-2 ${borderColor} p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300`}
      >
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div
              className={`w-10 h-10 ${iconColor} rounded-lg flex items-center justify-center mr-3 shadow-md`}
            >
              <span className="text-white font-bold text-lg">
                {title.includes("Text")
                  ? "📝"
                  : title.includes("Vector")
                  ? "🧠"
                  : "🔄"}
              </span>
            </div>
            <h3 className="font-bold text-lg text-gray-800">{title}</h3>
          </div>
          <div className="text-sm font-semibold text-gray-600 bg-white px-3 py-1 rounded-full shadow-sm">
            {results.data.hits.length} results
          </div>
        </div>
        <div className="space-y-3">
          {results.data.hits.slice(0, 3).map((hit, index) => (
            <div
              key={hit.document.id}
              className="bg-white p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1 mr-3">
                  <p className="font-semibold text-sm text-gray-900 leading-tight mb-1">
                    {hit.document.name}
                  </p>
                  <p className="text-xs text-gray-600 line-clamp-2">
                    {hit.document.description?.substring(0, 100)}
                    {hit.document.description?.length > 100 ? "..." : ""}
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-[#F0381A]">
                    ${hit.document.price?.toFixed(2)}
                  </div>
                  {hit.document.priceOld &&
                    hit.document.priceOld > hit.document.price && (
                      <div className="text-xs text-gray-500 line-through">
                        ${hit.document.priceOld.toFixed(2)}
                      </div>
                    )}
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {/* <span
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      index === 0
                        ? "bg-yellow-100 text-yellow-800 border border-yellow-200"
                        : index === 1
                        ? "bg-gray-100 text-gray-800 border border-gray-200"
                        : "bg-orange-100 text-orange-800 border border-orange-200"
                    }`}
                  >
                    #{index + 1}
                  </span> */}
                  {hit.text_match_info && hit.text_match_info.score && (
                    <span className="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded border">
                      Score:{" "}
                      {typeof hit.text_match_info.score === "number"
                        ? hit.text_match_info.score.toFixed(3)
                        : hit.text_match_info.score}
                    </span>
                  )}
                </div>
                <div className="text-xs text-gray-400 font-medium">
                  {hit.document.category_name}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 Sora mt-50">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-black mb-4">
            Vector Search Demo
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Experience the power of AI-driven semantic search. Compare
            traditional text search with vector search and hybrid approaches.
          </p>
        </div>

        {/* Interactive Search Bar */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4 text-black">
            Try Vector Search
          </h2>
          <VectorSearchBar />
        </div>

        {/* Example Queries */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4 text-black">
            Example Queries
          </h2>
          <p className="text-gray-600 mb-4">
            Try these example queries to see how vector search understands
            context and intent:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {exampleQueries.map((example, index) => (
              <div
                key={index}
                className="border border-gray-200 rounded-lg p-4 hover:border-[#F0381A] cursor-pointer transition-colors"
                onClick={() => handleExampleQuery(example.query)}
              >
                <p className="font-medium text-[#F0381A]">"{example.query}"</p>
                <p className="text-sm text-gray-600 mt-1">
                  {example.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Search Comparison */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-black">
              Search Comparison
            </h2>
            <div className="flex gap-2">
              <input
                type="text"
                value={demoQuery}
                onChange={(e) => setDemoQuery(e.target.value)}
                placeholder="Enter a search query to compare..."
                className="px-3 py-2 border border-gray-300 w-80 text-black rounded-lg"
              />
              <button
                onClick={() => setShowComparison(true)}
                disabled={demoQuery.length < 3}
                className="px-4 py-2 bg-[#F0381A] text-white rounded-lg disabled:bg-gray-300"
              >
                Compare
              </button>
            </div>
          </div>

          {showComparison && demoQuery && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {renderSearchResults(
                textResults,
                textLoading,
                "Text Search",
                "bg-gradient-to-br from-gray-50 to-gray-100",
                "bg-gradient-to-br from-gray-600 to-gray-700",
                "border-gray-300"
              )}
              {renderSearchResults(
                vectorResults,
                vectorLoading,
                "Vector Search",
                "bg-gradient-to-br from-purple-50 to-purple-100",
                "bg-gradient-to-br from-purple-600 to-purple-700",
                "border-purple-300"
              )}
              {renderSearchResults(
                hybridResults,
                hybridLoading,
                "Hybrid Search",
                "bg-gradient-to-br from-blue-50 to-blue-100",
                "bg-gradient-to-br from-blue-600 to-blue-700",
                "border-blue-300"
              )}
            </div>
          )}
        </div>

        {/* Information Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-gray-200 hover:border-gray-300 transition-all duration-300">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-gray-600 to-gray-700 rounded-lg flex items-center justify-center mr-3 shadow-md">
                <span className="text-white font-bold text-lg">📝</span>
              </div>
              <h3 className="text-lg font-bold text-gray-800">Text Search</h3>
            </div>
            <p className="text-gray-600 text-sm leading-relaxed">
              Traditional keyword-based search. Matches exact words and phrases
              in product names and descriptions.
            </p>
            <div className="mt-4 text-xs text-gray-500 bg-gray-50 px-3 py-2 rounded-lg">
              <strong>Best for:</strong> Exact model numbers, specific brands,
              precise specifications
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-purple-200 hover:border-purple-300 transition-all duration-300">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg flex items-center justify-center mr-3 shadow-md">
                <span className="text-white font-bold text-lg">🧠</span>
              </div>
              <h3 className="text-lg font-bold text-purple-700">
                Vector Search
              </h3>
            </div>
            <p className="text-gray-600 text-sm leading-relaxed">
              AI-powered semantic search that understands meaning and context.
              Finds products based on intent rather than exact keywords.
            </p>
            <div className="mt-4 text-xs text-purple-700 bg-purple-50 px-3 py-2 rounded-lg">
              <strong>Best for:</strong> Natural language queries, concept-based
              searches, discovering similar products
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-blue-200 hover:border-blue-300 transition-all duration-300">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center mr-3 shadow-md">
                <span className="text-white font-bold text-lg">🔄</span>
              </div>
              <h3 className="text-lg font-bold text-blue-700">Hybrid Search</h3>
            </div>
            <p className="text-gray-600 text-sm leading-relaxed">
              Combines the precision of text search with the intelligence of
              vector search for the best of both worlds.
            </p>
            <div className="mt-4 text-xs text-blue-700 bg-blue-50 px-3 py-2 rounded-lg">
              <strong>Best for:</strong> Comprehensive results, balanced
              relevance, most versatile option
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
