function generateHashEmbedding(text, dimensions = 384) {
  const embedding = new Array(dimensions).fill(0);

  // Simple hash-based approach
  for (let i = 0; i < text.length; i++) {
    const char = text.charCodeAt(i);
    const index = char % dimensions;
    embedding[index] += Math.sin(char * 0.1) * 0.1;
  }

  // Normalize the embedding
  const magnitude = Math.sqrt(
    embedding.reduce((sum, val) => sum + val * val, 0)
  );
  if (magnitude > 0) {
    for (let i = 0; i < embedding.length; i++) {
      embedding[i] /= magnitude;
    }
  }

  return embedding;
}

export async function generateEmbedding(text) {
  try {
    if (!text || typeof text !== "string" || text.trim() === "") {
      console.warn("Invalid or empty text provided for embedding");
      return generateHashEmbedding("default", 128);
    }
    try {
      if (
        typeof process !== "undefined" &&
        process.versions &&
        process.versions.node
      ) {
        const { pipeline } = await import("@xenova/transformers");

        const embeddingPipeline = await pipeline(
          "feature-extraction",
          "Xenova/all-MiniLM-L6-v2",
          {
            device: "cpu",
            dtype: "fp32",
          }
        );

        const output = await embeddingPipeline(text, {
          pooling: "mean",
          normalize: true,
        });

        if (!output || !output.data) {
          throw new Error("Invalid transformer output");
        }

        const embeddings = Array.from(output.data);

        if (!Array.isArray(embeddings) || embeddings.length === 0) {
          throw new Error("Invalid embeddings array");
        }

        console.log(
          `Generated transformer embedding for text: "${text.substring(
            0,
            50
          )}..." (${embeddings.length} dimensions)`
        );
        return embeddings;
      }
    } catch (transformerError) {
      console.warn(
        "Transformer embedding failed, using fallback:",
        transformerError.message
      );
    }

    // Fallback to hash-based embedding (use compact 128 dimensions)
    console.log(
      `Using hash-based embedding fallback for: "${text.substring(0, 50)}..."`
    );
    return generateHashEmbedding(text, 128);
  } catch (error) {
    console.error("Error generating embedding:", error);
    // Return a compact zero embedding as last resort
    return new Array(128).fill(0);
  }
}

export async function generateProductEmbedding(product) {
  try {
    const textParts = [];

    if (product.name) textParts.push(product.name);
    if (product.description) textParts.push(product.description);
    if (product.standardizedTitle) textParts.push(product.standardizedTitle);
    if (product.keywords && Array.isArray(product.keywords)) {
      textParts.push(product.keywords.join(" "));
    }
    if (product.category_name) textParts.push(product.category_name);

    const combinedText = textParts.join(" ").trim();

    if (!combinedText) {
      console.warn(
        "No text content found for product embedding, using zero embedding"
      );
      return new Array(128).fill(0); // Use compact dimensions
    }

    return await generateEmbedding(combinedText);
  } catch (error) {
    console.error("Error generating product embedding:", error);
    return new Array(128).fill(0); // Use compact dimensions
  }
}

export async function generateProductEmbeddingsBatch(products, batchSize = 5) {
  const results = [];

  console.log(
    `Generating embeddings for ${products.length} products in batches of ${batchSize}`
  );

  for (let i = 0; i < products.length; i += batchSize) {
    const batch = products.slice(i, i + batchSize);
    console.log(
      `Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(
        products.length / batchSize
      )}`
    );

    const batchPromises = batch.map(async (product) => {
      try {
        const embedding = await generateProductEmbedding(product);
        return {
          ...product,
          embedding,
        };
      } catch (error) {
        console.error(
          `Failed to generate embedding for product ${product.id}:`,
          error
        );
        return {
          ...product,
          embedding: new Array(128).fill(0),
        };
      }
    });

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);

    // Small delay between batches to avoid overwhelming the system
    if (i + batchSize < products.length) {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
  }

  console.log(`Completed embedding generation for ${results.length} products`);
  return results;
}

export async function isTransformersAvailable() {
  try {
    await import("@xenova/transformers");
    return true;
  } catch (error) {
    return false;
  }
}
